# Rhys AI 智能体工作规范使用指南

## 简介

这是一套基于最新AI研究洞察的智能体工作规范，旨在解决AI过度自信和"拍马屁"问题，确保输出的可靠性和透明度。

## 核心理念

基于Anthropic团队的研究发现：不要轻信AI用文字写下的"思考过程"，真正决定输出的是内部推理电路。因此我建立了严格的校准和验证机制来适配官方的核心think内容工作机制，具体内容有兴趣可以去Youtube上搜索"Anthropic"观看他们的视频，title：Interpretability: Understanding how AI models think。

## 主要文件说明

### # 🧠 Rhys AI 智能体工作规范 v2.0.md   （这个常用语augment、trae智能体、qoder等）
这是核心工作规范文件，定义了AI的工作模式和质量标准。

**五个工作模式：**
- RESEARCH：信息收集和理解现状
- INNOVATE：方案设计和技术选型  
- PLAN：详细规划和创建检查清单
- EXECUTE：严格执行计划（需用户明确确认）
- REVIEW：全面质量审查和验证

**关键特性：**
- 每个模式完成后会暂停询问用户是否继续
- 强制要求把握度评估（0-100%）
- 低于80%把握度时主动寻求外部验证
- 禁止使用"最佳"、"完美"等绝对化表述

### Subagent 专业团队（适用于Claude code工具使用）

位于 `.claude/agents/` 目录下的专业AI助手：

**product-manager.md - 产品经理**
- 负责需求分析和PRD文档输出
- 具备商业洞察和用户研究能力
- 每个分析都包含把握度评估和关键假设

**designer.md - 设计师**
- 负责UI/UX设计和交互原型
- 涵盖用户体验和视觉设计
- 提供设计决策的把握度和验证建议

**developer.md - 开发工程师**
- 负责全栈技术实现
- 支持前端、后端、游戏等各类开发
- 包含技术风险评估和权衡分析

**verification-agent.md - 验证专家**
- 专门质疑和验证其他Agent输出
- 防止过度自信和确认偏误
- 提供独立的第三方验证视角

**code-reviewer.md - 代码审查专家**
- 专注代码质量和配置安全
- 特别关注生产环境可靠性
- 采用"证明其安全"的审查态度

## 使用方法

### 基础使用
1. 将 `# 🧠 Rhys AI 智能体工作规范 v2.0.md ` 内容添加到AI系统提示词中
2. 将 `.claude/agents/` 目录下的文件放置在Claude code （C:\Users\<USER>\.claude\agents），CLAUDE.md 则放于Claude code （C:\Users\<USER>\.claude）目录下全局调用，项目级则在项目文件中使用即可
3. 根据需要调用相应的专业Agent

### 命令调用
- `/产品` - 启动产品经理进行需求分析
- `/设计` - 启动设计师进行UI/UX设计
- `/开发` - 启动开发工程师进行技术实现
- `/验证` - 启动验证专家进行质疑验证
- `/审查` - 启动代码审查专家检查代码质量

### 工作流程
1. 用户提出需求
2. AI进入RESEARCH模式收集信息
3. 询问用户是否进入INNOVATE模式
4. 逐步推进到PLAN模式制定详细计划
5. 用户明确确认后进入EXECUTE模式
6. 最后进行REVIEW模式质量检查

## 输出格式特点

每个Agent的输出都包含：

**把握度评估**
```
把握度：75%
关键假设：
1. 用户量预估基于类似产品数据
2. 技术实现复杂度为中等水平
3. 开发周期按标准敏捷流程计算
```

**不确定性说明**
```
不确定性：
- 具体用户行为模式需要实际测试验证
- 第三方API的稳定性和性能表现
- 团队技术能力与预期的匹配度
```

**验证建议**
```
建议验证：
1. 进行用户访谈确认需求优先级
2. 技术预研验证关键实现方案
3. 制作原型进行可用性测试
```

## 核心优势

**防止AI"乱扯"**
- 强制把握度评估避免过度自信
- 验证专家独立质疑关键结论
- 透明化推理过程和关键假设

**提升输出质量**
- 多角度交叉验证重要决策
- 明确区分事实、推理和假设
- 提供具体可执行的验证方法

**保持用户控制**
- 每个阶段都需要用户确认
- 低把握度时主动寻求指导
- 承认分析局限性和不确定性

## 注意事项

1. **执行模式需要明确确认**：只有EXECUTE模式需要用户明确说"进入执行模式"
2. **把握度阈值**：低于80%的把握度会触发额外验证流程
3. **避免绝对化表述**：系统会自动检测并建议替代表达
4. **保持质疑态度**：验证专家会对所有输出进行建设性质疑

## 适用场景

- 产品开发项目的全流程管理
- 技术方案设计和实现
- 需要高质量决策的复杂任务
- 对AI输出可靠性有较高要求的场景

## 技术要求

- 支持文件读取和角色切换的AI系统
- 能够执行多轮对话和状态管理
- 具备代码生成和项目管理能力

这套规范仅为个人日常使用，建议在设计和审查内容时采用GPT-5或者gemini 2.5 PRO等其他模型进行，避免输入token太多导致的浪费，同时通过严格的校准机制确保AI输出的质量和可信度。
